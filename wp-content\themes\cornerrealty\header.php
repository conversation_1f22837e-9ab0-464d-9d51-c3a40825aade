<?php

/**
 * The header.
 *
 * This is the template that displays all of the <head> section and everything up until main.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package WordPress
 * @subpackage Twenty_Twenty_One
 * @since Twenty Twenty-One 1.0
 */

global $post;
$meta      = get_field_objects($post->ID);
$home_meta = get_field_objects(2);
?>
<!doctype html>
<html <?php language_attributes(); ?> <?php twentytwentyone_the_html_classes(); ?>>

<head>
  <meta charset="<?php bloginfo('charset'); ?>" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />

  <!-- fonts  -->

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
  <!-- Vendor CSS Files -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">
  <link href="<?php echo esc_url(get_template_directory_uri()); ?>/assets/css/animate.min.css" rel="stylesheet">
  <link href="<?php echo esc_url(get_template_directory_uri()); ?>/assets/css/bootstrap.min.css" rel="stylesheet">
  <link href="<?php echo esc_url(get_template_directory_uri()); ?>/assets/css/bootstrap-icon.css" rel="stylesheet">
  <link href="<?php echo esc_url(get_template_directory_uri()); ?>/assets/css/boxicons.min.css" rel="stylesheet">

  <link rel="stylesheet" href="<?php echo esc_url(get_template_directory_uri()); ?>/assets/css/owl.carousel.min.css" />
  <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css" />


  <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>

  <!-- ======= Header ======= -->
  <header id="header" class="fixed-top">
    <div class="d-flex align-items-center">
      <a href="<?php bloginfo('url') ?>" class="logo me-auto" data-aos="fade-right"><img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/logo.svg" alt="" class="img-fluid"></a>

      <i class="mobile-nav-toggle mobile-nav-show fa-solid fa-bars"></i>
      <i class="mobile-nav-toggle mobile-nav-hide d-none fa-solid fa-xmark"></i>

      <nav id="navbar" class="navbar order-last order-lg-0" data-aos="fade-left">
        <?php
        if (has_nav_menu('header')) :
          wp_nav_menu(array(
            'theme_location' => 'header',
            'menu_class' => ''
          ));
        endif;
        ?>

        <?php if (!is_user_logged_in()) : ?>
          <!-- Add Login link for testing -->
          <ul style="margin: 0; padding: 0; display: flex; list-style: none; align-items: center; gap: 40px;">
            <li><a href="#login" style="color: #ffffff; text-transform: uppercase; font-weight: 500; font-size: 16px;">Login</a></li>
          </ul>
        <?php else : ?>
          <!-- Show logout link if logged in -->
          <ul style="margin: 0; padding: 0; display: flex; list-style: none; align-items: center; gap: 40px;">
            <li><a href="<?php echo home_url('/virtual-data-room/'); ?>" style="color: #ffffff; text-transform: uppercase; font-weight: 500; font-size: 16px;">Virtual Data Room</a></li>
            <li><a href="<?php echo wp_logout_url(home_url()); ?>" style="color: #ffffff; text-transform: uppercase; font-weight: 500; font-size: 16px;">Logout</a></li>
          </ul>
        <?php endif; ?>
      </nav>

  </header>

  <!-- Login Modal -->
  <div id="login-modal" class="login-modal-overlay" style="display: none;">
    <div class="login-modal-content">
      <div class="login-modal-header">
        <h2>Login</h2>
        <button type="button" class="login-modal-close">&times;</button>
      </div>
      <div class="login-modal-body">
        <form id="login-form" method="post">
          <div id="login-error" class="login-error" style="display: none;"></div>

          <div class="form-group">
            <label for="login-username">Username or E-mail</label>
            <input type="text" id="login-username" name="username" required>
          </div>

          <div class="form-group">
            <label for="login-password">Password</label>
            <input type="password" id="login-password" name="password" required>
          </div>

          <div class="form-group checkbox-group">
            <label>
              <input type="checkbox" id="login-remember" name="remember">
              Keep me signed in
            </label>
          </div>

          <div class="form-group">
            <button type="submit" id="login-submit" class="login-btn">Log In</button>
          </div>
        </form>
      </div>
    </div>
  </div>