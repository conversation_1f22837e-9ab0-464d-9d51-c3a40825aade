<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Modal Test</title>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #1e396c;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            background: #1a3159;
        }
        
        /* Include the login modal CSS */
        .login-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-modal-content {
            background: #fff;
            width: 90%;
            max-width: 450px;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            position: relative;
        }

        .login-modal-header {
            background: #1e396c;
            color: #fff;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .login-modal-header h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #fff;
            line-height: 1;
        }

        .login-modal-close {
            background: none;
            border: none;
            color: #fff;
            font-size: 28px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.3s ease;
        }

        .login-modal-close:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .login-modal-body {
            padding: 30px 25px;
        }

        .login-modal-body .form-group {
            margin-bottom: 20px;
        }

        .login-modal-body .form-group:last-child {
            margin-bottom: 0;
        }

        .login-modal-body label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #1e396c;
            font-size: 14px;
        }

        .login-modal-body input[type="text"],
        .login-modal-body input[type="password"] {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .login-modal-body input[type="text"]:focus,
        .login-modal-body input[type="password"]:focus {
            outline: none;
            border-color: #1e396c;
            box-shadow: 0 0 0 2px rgba(30, 57, 108, 0.1);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
        }

        .checkbox-group label {
            display: flex;
            align-items: center;
            margin-bottom: 0;
            cursor: pointer;
            font-size: 13px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
            width: auto;
        }

        .login-btn {
            width: 100%;
            background: #1e396c;
            color: #fff;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .login-btn:hover {
            background: #1a3159;
        }

        .login-error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid #f5c6cb;
        }

        body.modal-open {
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Login Modal Test</h1>
        <p>Click the button below to test the login modal:</p>
        <a href="#login" class="test-button">Open Login Modal</a>
        
        <h2>Instructions:</h2>
        <ol>
            <li>Click the "Open Login Modal" button</li>
            <li>The modal should appear with a half-screen overlay</li>
            <li>Try entering test credentials</li>
            <li>The modal should close when clicking the X or outside the modal</li>
            <li>Press ESC key to close the modal</li>
        </ol>
    </div>

    <!-- Login Modal -->
    <div id="login-modal" class="login-modal-overlay" style="display: none;">
        <div class="login-modal-content">
            <div class="login-modal-header">
                <h2>Login</h2>
                <button type="button" class="login-modal-close">&times;</button>
            </div>
            <div class="login-modal-body">
                <form id="login-form" method="post">
                    <div id="login-error" class="login-error" style="display: none;"></div>
                    
                    <div class="form-group">
                        <label for="login-username">Username or E-mail</label>
                        <input type="text" id="login-username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <input type="password" id="login-password" name="password" required>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label>
                            <input type="checkbox" id="login-remember" name="remember">
                            Keep me signed in
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" id="login-submit" class="login-btn">Log In</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        jQuery(document).ready(function($) {
            // Open login modal when login link is clicked
            $(document).on('click', 'a[href="#login"]', function(e) {
                e.preventDefault();
                $('#login-modal').fadeIn(300);
                $('body').addClass('modal-open');
            });

            // Close modal when clicking close button or overlay
            $(document).on('click', '.login-modal-close, .login-modal-overlay', function(e) {
                if (e.target === this) {
                    $('#login-modal').fadeOut(300);
                    $('body').removeClass('modal-open');
                }
            });

            // Close modal with Escape key
            $(document).keydown(function(e) {
                if (e.keyCode === 27) { // ESC key
                    $('#login-modal').fadeOut(300);
                    $('body').removeClass('modal-open');
                }
            });

            // Handle login form submission (test version)
            $('#login-form').on('submit', function(e) {
                e.preventDefault();
                
                var $submitBtn = $('#login-submit');
                var $errorMsg = $('#login-error');
                
                // Clear previous errors
                $errorMsg.hide().text('');
                
                // Show loading state
                $submitBtn.prop('disabled', true).text('Logging in...');
                
                // Simulate login process
                setTimeout(function() {
                    var username = $('#login-username').val();
                    var password = $('#login-password').val();
                    
                    if (username === 'test' && password === 'test') {
                        alert('Login successful! (This is a test)');
                        $('#login-modal').fadeOut(300);
                        $('body').removeClass('modal-open');
                    } else {
                        $errorMsg.text('Invalid username or password. Try "test" / "test"').show();
                    }
                    
                    $submitBtn.prop('disabled', false).text('Log In');
                }, 1000);
            });
        });
    </script>
</body>
</html>
