{"name": "nsp-code/post-types-order", "description": "Posts Order and Post Types Objects Order using a Drag and Drop Sortable javascript capability", "keywords": ["post order", "posts order", "sort", "post sort", "posts sort", "post type order", "custom order", "admin posts order"], "homepage": "http://www.nsp-code.com/", "authors": [{"name": "Nsp Code", "email": "<EMAIL>", "homepage": "http://www.nsp-code.com/"}], "type": "wordpress-plugin", "repositories": [{"type": "composer", "url": "https://wpackagist.org", "only": ["wpackagist-plugin/*", "wpackagist-theme/*"]}], "require": {"composer/installers": "~1.0"}}