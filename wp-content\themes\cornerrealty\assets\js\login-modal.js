jQuery(document).ready(function($) {
    // Open login modal when login link is clicked
    $(document).on('click', 'a[href="#login"]', function(e) {
        e.preventDefault();
        $('#login-modal').fadeIn(300);
        $('body').addClass('modal-open');
    });

    // Close modal when clicking close button or overlay
    $(document).on('click', '.login-modal-close, .login-modal-overlay', function(e) {
        if (e.target === this) {
            $('#login-modal').fadeOut(300);
            $('body').removeClass('modal-open');
        }
    });

    // Close modal with Escape key
    $(document).keydown(function(e) {
        if (e.keyCode === 27) { // ESC key
            $('#login-modal').fadeOut(300);
            $('body').removeClass('modal-open');
        }
    });

    // Handle login form submission
    $('#login-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $form.find('#login-submit');
        var $errorMsg = $('#login-error');
        
        // Clear previous errors
        $errorMsg.hide().text('');
        
        // Show loading state
        $submitBtn.prop('disabled', true).text('Logging in...');
        
        // Get form data
        var formData = {
            action: 'ajax_login',
            username: $('#login-username').val(),
            password: $('#login-password').val(),
            remember: $('#login-remember').is(':checked'),
            security: ajax_login_object.nonce
        };
        
        // AJAX request
        $.ajax({
            url: ajax_login_object.ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Redirect to Virtual Data Room
                    window.location.href = response.data.redirect_url;
                } else {
                    // Show error message
                    $errorMsg.text(response.data.message).show();
                    $submitBtn.prop('disabled', false).text('Log In');
                }
            },
            error: function() {
                $errorMsg.text('An error occurred. Please try again.').show();
                $submitBtn.prop('disabled', false).text('Log In');
            }
        });
    });
});
