<?php
/**
 * Template Name: Virtual Data Room
 * 
 * The template for displaying the Virtual Data Room page
 *
 * @package WordPress
 * @subpackage Corner_Realty
 */

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(home_url());
    exit;
}

get_header();
?>

<div class="pageMargin">
    <div class="pageBanner" style="background: url('<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/videoposter.jpg') center center/cover;">
        <div class="pageBannerInner">
            <div class="container-xl">
                <div class="row">
                    <div class="col-12">
                        <h1>Virtual Data Room</h1>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="virtual-data-room-content py-6">
        <div class="container-xl">
            <div class="row">
                <div class="col-12">
                    <div class="virtual-data-room-intro mb-5">
                        <h2>Welcome to the Virtual Data Room</h2>
                        <p>Access exclusive documents and files. This area is restricted to authorized users only.</p>
                    </div>

                    <?php
                    // Query Virtual Data Room posts
                    $vdr_query = new WP_Query(array(
                        'post_type' => 'virtual-data-room',
                        'posts_per_page' => -1,
                        'post_status' => 'publish',
                        'orderby' => 'date',
                        'order' => 'DESC'
                    ));

                    if ($vdr_query->have_posts()) : ?>
                        <div class="virtual-data-room-files">
                            <div class="row">
                                <?php while ($vdr_query->have_posts()) : $vdr_query->the_post(); ?>
                                    <div class="col-lg-4 col-md-6 col-12 mb-4">
                                        <div class="vdr-file-card">
                                            <?php if (has_post_thumbnail()) : ?>
                                                <div class="vdr-file-thumbnail">
                                                    <?php the_post_thumbnail('medium', array('class' => 'img-fluid')); ?>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="vdr-file-content">
                                                <h3><?php the_title(); ?></h3>
                                                <div class="vdr-file-excerpt">
                                                    <?php the_excerpt(); ?>
                                                </div>
                                                <div class="vdr-file-meta">
                                                    <small class="text-muted">
                                                        Published: <?php echo get_the_date(); ?>
                                                    </small>
                                                </div>
                                                <div class="vdr-file-actions">
                                                    <a href="<?php the_permalink(); ?>" class="btn btn-primary">
                                                        View Details
                                                    </a>
                                                    
                                                    <?php
                                                    // Check for file attachments
                                                    $attachments = get_attached_media('', get_the_ID());
                                                    if ($attachments) :
                                                        foreach ($attachments as $attachment) :
                                                            $file_url = wp_get_attachment_url($attachment->ID);
                                                            $file_name = get_the_title($attachment->ID);
                                                    ?>
                                                        <a href="<?php echo esc_url($file_url); ?>" 
                                                           class="btn btn-outline-primary" 
                                                           download="<?php echo esc_attr($file_name); ?>">
                                                            Download
                                                        </a>
                                                    <?php 
                                                        endforeach;
                                                    endif; 
                                                    ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        </div>
                    <?php else : ?>
                        <div class="no-files-message">
                            <div class="alert alert-info">
                                <h4>No Files Available</h4>
                                <p>There are currently no files available in the Virtual Data Room. Please check back later or contact us if you believe this is an error.</p>
                            </div>
                        </div>
                    <?php endif; 
                    
                    wp_reset_postdata(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.virtual-data-room-content {
    background: #fff;
}

.virtual-data-room-intro {
    text-align: center;
    padding: 40px 0;
}

.virtual-data-room-intro h2 {
    color: var(--primary);
    font-size: 40px;
    line-height: 44px;
    font-weight: 700;
    margin-bottom: 20px;
}

.virtual-data-room-intro p {
    color: var(--secondary);
    font-size: 18px;
    line-height: 1.6;
}

.vdr-file-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.vdr-file-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.vdr-file-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.vdr-file-content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.vdr-file-content h3 {
    color: var(--primary);
    font-size: 18px;
    line-height: 22px;
    font-weight: 600;
    margin-bottom: 15px;
}

.vdr-file-excerpt {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    flex: 1;
}

.vdr-file-meta {
    margin-bottom: 15px;
}

.vdr-file-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid;
    display: inline-block;
}

.btn-primary {
    background: var(--primary);
    color: #fff;
    border-color: var(--primary);
}

.btn-primary:hover {
    background: #1a3159;
    border-color: #1a3159;
    color: #fff;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:hover {
    background: var(--primary);
    color: #fff;
}

.no-files-message {
    text-align: center;
    padding: 60px 20px;
}

.alert {
    padding: 20px;
    border-radius: 8px;
    border: 1px solid;
}

.alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert h4 {
    color: #0c5460;
    margin-bottom: 10px;
}
</style>

<?php get_footer(); ?>
