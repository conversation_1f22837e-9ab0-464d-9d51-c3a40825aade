@charset "UTF-8"; /*
Theme Name:Corner Realty Wordpress Theme
Author:Focused CRE
Author URI:https://focusedcre.com
Description:Custom wordpress theme for Corner Realty.
Version:1
*/:root{--primary:#1e396c; --secondary:#afafaf;}
html,body{overflow-x:hidden; width:100%;}
body{font-family:'Poppins',sans-serif;}
#mainWebContainer{overflow-x:hidden; position:relative;}
/************************* fonts,buttons,icons and text blocks styles**********************************/


h1{font-size:50px; color:#000000; line-height:50px;}
h2{font-size:44px; color:#000000; line-height:44px;}
h3{font-size:24px; color:#000000; line-height:26px;}
h4{font-size:20px; color:#000000; line-height:30px;}
h5{font-size:16px; color:#000000; line-height:24px;}
h6{font-size:14px; color:#000000; line-height:18px;}
.heading-inline{display:inline !important;}
a{color:#000000; font-weight:400; text-decoration:none; -webkit-transition:0.3s ease-in-out !important; -moz-transition:0.3s ease-in-out !important; -ms-transition:0.3s ease-in-out !important; -o-transition:0.3s ease-in-out !important; transition:0.3s ease-in-out !important;}
a:hover{color:#000000; text-decoration:none;}
a:focus{text-decoration:none; outline:none;}
ul{margin:0; padding:0;}
ul li{list-style:none;}
img{image-rendering:-webkit-optimize-contrast;}
#map{height:400px;}
/*--------------------------------------------------------------
# Top Bar
--------------------------------------------------------------*/


#topbar{background:#306178; height:40px; font-size:14px; border-bottom:1px solid #b49e3f; transition:all 0.5s;}
#topbar.topbar-scrolled{top:-40px;}
#topbar .contact-info a{color:#ffffff; font-size:14px; line-height:14px; font-weight:500;}
#topbar .contact-info a:hover{color:#ffffff;}
#topbar .contact-info a i{color:#ffffff;}
.cusBtn{position:relative; padding:18px 40px; font-weight:700; display:inline-block; text-transform:uppercase; font-size:17px; line-height:20px; color:#fff; border:1px solid #ffffff; letter-spacing:1px; overflow:hidden; transition:all 0.4s ease !important; z-index:1;}
.cusBtn::before{content:""; position:absolute; left:-100%; top:0; width:100%; height:100%; background:#fff; z-index:-1; transition:all 0.4s ease;}
.cusBtn:hover{color:var(--primary); border-color:var(--primary);}
.cusBtn:hover::before{left:0;}
.pageMargin{margin-top:100px;}
/*--------------------------------------------------------------
# Header 
--------------------------------------------------------------*/


#header{background-color:transparent; transition:all 0.4s; padding:25px 10%;}
#header.header-scrolled{background:var(--primary); top:0;}
#header .logo img{width:225px;}
#header.header-scrolled .navbar li a,.navbar li a:focus{color:#ffffff;}
/* #header.header-scrolled .navbar li a::before{background:#ffffff;}
*/

.navbar li:last-child a{position:relative; padding:13px 35px; display:inline-block; text-transform:uppercase; font-size:16px; line-height:16px; background:var(--primary); color:#ffffff; overflow:hidden; border:1px solid var(--primary); transition:all 0.4s ease !important; z-index:1;}
.navbar li:last-child a::before{content:""; background:url('assets/img/call-us.svg') no-repeat center center; margin-right:10px; width:17px; height:17px; display:inline-block; vertical-align:middle;}
#header.header-scrolled .navbar li:last-child a{border-color:#fff;}
/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Desktop Navigation
--------------------------------------------------------------*/


@media (min-width:1024px){
  .navbar{padding:0;}
  .navbar ul{margin:0; padding:0; display:flex; list-style:none; align-items:center; gap:40px;}
  .navbar li{position:relative; line-height:16px;}
  .navbar li a,.navbar li a:focus{color:#ffffff; text-transform:uppercase; font-weight:500; font-size:16px; line-height:17px; position:relative;}
  .navbar li a:hover,.navbar li.current-menu-item a,.menu-item-has-children a{font-weight:700;}
  .navbar li .sub-menu{display:block; margin-top:5px; padding-left:0; list-style:none;}
  .navbar li .sub-menu li{margin:0; line-height:initial;}
  .navbar li .sub-menu a{text-transform:none; font-size:14px; line-height:15px; font-weight:400; color:var(--secondary);}
  .navbar li .sub-menu a:hover{color:var(--primary);}
}
@media (min-width:1024px){
  .mobile-nav-show,.mobile-nav-hide{display:none;}
}
/*--------------------------------------------------------------
# Mobile Navigation
--------------------------------------------------------------*/


@media (max-width:1023px){
  #header{padding:15px 0;}
  #header .logo img{width:200px;}
  .navbar{position:fixed; top:0; right:-100%; width:100%; max-width:400px; bottom:0; transition:0.3s; z-index:9997;}
  .navbar ul{position:absolute; inset:0; padding:50px 0 10px 0; margin:0; background:#000; opacity:0.95; overflow-y:auto; transition:0.3s; z-index:9998;}
  .navbar a,.navbar a:focus{display:flex; align-items:center; justify-content:space-between; padding:10px 20px; font-size:15px; font-weight:600; color:rgba(255,255,255,0.7); white-space:nowrap; transition:0.3s;}
  .navbar a i,.navbar a:focus i{font-size:12px; line-height:0; margin-left:5px;}
  .navbar a:hover,.navbar .current-menu-item a,.navbar .current-menu-item:focus a,.navbar li:hover > a{color:#fff;}
  /* Mobile submenus - now visible by default */
  
  .navbar .menu-item-has-children ul.sub-menu,.navbar .menu-item-has-children .menu-item-has-children ul.sub-menu{position:static; display:block; padding:10px 0; margin:10px 20px; background-color:rgba(20,35,51,0.6);}
  .navbar .menu-item-has-children > .submenu-active,.navbar .menu-item-has-children .menu-item-has-children > .submenu-active{display:block;}
  .mobile-nav-show{font-size:28px; cursor:pointer; line-height:0; transition:0.5s; color:#000; padding-right:30px;}
  .mobile-nav-hide{color:rgba(255,255,255,0.9); font-size:28px; cursor:pointer; line-height:0; transition:0.5s; position:fixed; right:15px; top:25px; z-index:9999;}
  .mobile-nav-active{overflow:hidden;}
  .mobile-nav-active .navbar{right:0;}
  .mobile-nav-active .navbar:before{content:""; position:fixed; inset:0; background:#000000; opacity:0.7; z-index:9996;}
}
@media (min-width:1023px){
  .mobile-nav-show,.mobile-nav-hide{display:none !important;}
}
.sub-menu-toggle{display:none !important;}
.id-scrool-fix{position:relative; top:-100px;}
/*--------------------------------------------------------------
# Video Hero Section Start
--------------------------------------------------------------*/


.video-hero-sec{position:relative; overflow:hidden;}
.video-hero-sec:after{content:""; position:absolute; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,.5); display:block;}
.video-hero-sec-inner{height:100vh; overflow:hidden; position:relative;}
.video-hero-sec-inner video{position:fixed; top:0; left:0; width:100%; height:100%; object-fit:cover; z-index:-1;}
.video-hero-sec .video-hero-sec-text{top:50%; left:50%; transform:translate(-50%,-50%); width:1140px; max-width:100%; z-index:1; position:absolute;text-align:center;}
.video-hero-sec .video-hero-sec-text h1{color:#ffffff; font-size:62px; line-height:62px; font-weight:700;}
.video-hero-sec .video-hero-sec-text h3{color:#ffffff; font-size:14px; line-height:15px; font-weight:600; text-transform:uppercase;}
.video-hero-sec a{margin-top:40px;}
/*--------------------------------------------------------------
# Video Hero Section End
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Home About Us
--------------------------------------------------------------*/


.aboutUs{padding:80px 0 80px; position:relative; z-index:1; overflow:hidden; background-color:#fff;}
.aboutUs::before {
    content: "";
    position: absolute;
    top: 0;
    right: -300px;
    width: 100%;
    height: 100%;
    background: url(assets/img/logo-cut-about.svg) top right no-repeat;
    z-index: -1;
    display: block;
    animation: gasWave 3s ease-in-out infinite;
}

@keyframes gasWave {
    0% {
        transform: translateX(0px) skewX(0deg);
        opacity: 0.95;
    }
    25% {
        transform: translateX(-6px) skewX(1deg);
        opacity: 1;
    }
    50% {
        transform: translateX(0px) skewX(0deg);
        opacity: 0.95;
    }
    75% {
        transform: translateX(6px) skewX(-1deg);
        opacity: 1;
    }
    100% {
        transform: translateX(0px) skewX(0deg);
        opacity: 0.95;
    }
}


.aboutUs h3{color:var(--secondary); text-transform:uppercase; font-size:14px; line-height:15px; font-weight:600; margin-bottom:10px;}
.aboutUs h2{color:var(--primary); font-size:40px; line-height:44px; font-weight:700; margin-bottom:30px;}
.aboutUs h4{color:var(--secondary); font-size:28px; line-height:1.2; font-weight:600; margin-bottom:30px;}
.aboutUs p{color:var(--primary); font-size:21px; font-weight:500; margin-bottom:0;}
/*--------------------------------------------------------------
# Home About Us
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Home Counter
--------------------------------------------------------------*/

.counterStats{background:var(--primary); padding:80px 0 30px;}
.counterStatsHead{margin-bottom:60px;}
.counterStatsHead h3{color:#ffffff; text-transform:uppercase; font-size:14px; line-height:15px; font-weight:600; margin-bottom:10px;}
.counterStatsHead h2{color:#ffffff; font-size:40px; line-height:44px; font-weight:700; margin-bottom:30px;}
.counterStats .counterStatsInner{flex-wrap:wrap;}
.counterStats .counterStatsInner .counterBox{flex:33.33%; text-align:center; padding-block:40px;}
.counterStats .counterStatsInner .counterBox:nth-of-type(2){border-inline:1px solid #fff;}
.counterStats .counterStatsInner .counterBox img{width:40px; height:auto; margin:0 auto 30px;}
.counterStats .counterStatsInner .counterBox h3{color:#ffffff; font-size:72px; line-height:72px; font-weight:600; text-transform:uppercase;}
.counterStats .counterStatsInner .counterBox p{color:#ffffff; text-align:center; font-size:14px; line-height:22px; margin-bottom:0; text-transform:uppercase;}
.counterStatsFoot{text-align:end; margin-top:60px;}
.counterStatsFoot p{color:#ffffff; font-size:13px; line-height:22px; margin-bottom:0;}
/*--------------------------------------------------------------
# Home Counter
--------------------------------------------------------------*/


/*--------------------------------------------------------------
# Home Why Us
--------------------------------------------------------------*/


.whyUs{padding:80px 0; background:#fff;}
.whyUsHead{margin-bottom:80px; text-align:center;}
.whyUsHead h2{color:var(--primary); font-size:40px; line-height:44px; font-weight:700; margin-bottom:30px;}
.whyUsHead p{color:var(--secondary); font-size:21px; font-weight:600; margin-bottom:0; padding-inline:10%;}
.whyUsHead p:last-child{color:var(--primary);}
.features h3{color:var(--primary); font-size:21px; line-height:24px; font-weight:700;}
.features p{font-size:14px; margin-bottom:0; color:#575351;}
.features .grid{display:grid; grid-template-columns:1fr 1fr; grid-template-rows:3fr 2fr; gap:20px; position:relative;}
.features .box{text-align:right; padding-inline:25%;}
.box.top-right h3,.box.bottom-right h3{text-align:left;}
.features .whyUsIcon{position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); width:380px; max-width:100%;}
.features .box p{text-align:left;}
.whyUsIcon svg{width:100%; height:auto;}
.whyUsIconCircle{transition:all 0.3s ease;}
.whyUsIconCircle:hover{transform:translateY(-5px);}
.whyUsIconCircle circle{transition:all 0.3s ease;}
.whyUsIconCircle:hover .cls-3{fill:#e3e3e3;}
/*--------------------------------------------------------------
# Home News Letter
--------------------------------------------------------------*/


.newsLetter{padding:60px 0; background:var(--primary); padding-inline:10%;}
.newsLetter h2{color:#ffffff; font-size:40px; line-height:44px; font-weight:700;}
.newsletterForm p{margin:0;}
.newsletterForm .form-fields{margin-bottom:0; border:none; padding:15px 14px; background:rgb(255 255 255 / 50%);}
.newsletterForm .form-fields:focus{border:none; outline:none;}
.newsletterForm ::placeholder{color:#ffffff !important; text-transform:initial;}
.newsletterForm .wpcf7-submit{padding:15px 30px; font-size:16px; line-height:19px; font-weight:600; text-transform:uppercase; border:1px solid #fff; background:transparent; color:#fff; transition:all 0.3s ease !important; cursor:pointer;}
.newsletterForm  .wpcf7-submit:hover{background:#fff; color:#000;}
.newsletterForm .wpcf7-spinner{position: absolute;}
/*--------------------------------------------------------------
# About
--------------------------------------------------------------*/
#aboutMain .aboutUs p{margin-bottom:1rem;}
.whatSetsUsApart{position:relative; overflow:hidden; padding:100px 0;}
.whatSetsUsApart::before{content:""; position:absolute; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:1;}
.whatSetsUsApart video{object-fit:cover; width:100%; height:100%; position:fixed; top:0; left:0; z-index:0;}
.whatSetsUsApartHead{margin-bottom:60px;}
.whatSetsUsApartHead h3{color:#ffffff; text-transform:uppercase; font-size:14px; line-height:15px; font-weight:600; margin-bottom:10px;}
.whatSetsUsApartHead h2{color:#ffffff; font-size:40px; line-height:44px; font-weight:700; margin-bottom:30px;}
.whatSetsUsApartInner{display:flex; justify-content:center; align-items:center; flex-wrap:wrap; gap:20px; z-index:2; position:relative;}
.whatSetsUsApartBox{text-align:center; padding:40px 30px; flex:1 1 250px; border:none;}
.whatSetsUsApartBox h3{color:#ffffff; font-size:32px; line-height:32px; font-weight:600; text-transform:uppercase;}
.whatSetsUsApartBox.middle{border-inline:1px solid #fff;}
.coreStrength{padding:80px 0; background:#afafaf; position:relative;}
.coreStrengthInner{display:flex; align-items:stretch; flex-wrap:wrap;}
.coreStrengthInnerBox{flex:1 1 250px; background:#fff; padding:40px 40px 60px; text-align:center; transition:all 0.4s ease; position:relative; border-right:2px solid #afafaf; cursor:pointer; box-shadow:0 0 0 rgba(0,0,0,0);}
.coreStrengthInnerBox:before{content:""; position:absolute; width:calc(100% - 40px); height:1px; background:#d3d3d3; bottom:30px; left:50%; transform:translate(-50%,-50%);}
.coreStrengthInnerBoxIcon{margin-bottom:30px; width:85px; height:85px; display:flex; justify-content:center; align-items:center; margin:0 auto; background-color:#959595; border-radius:50%; transition:all 0.4s ease;}
.coreStrengthInnerBox img{width:40px; filter:brightness(0) invert(1); transition:all 0.4s ease;}
.coreStrengthInnerBoxContent h3{font-size:18px; line-height:22px; margin-bottom:30px; margin-top:30px; font-weight:600; color:#2d2c2b; transition:color 0.4s ease;}
.coreStrengthInnerBoxContent p{font-size:15px; line-height:22px; margin-bottom:0; color:#2d2c2b; transition:color 0.4s ease;}
.coreStrengthInnerBox:hover{background:var(--primary); color:#fff; transform:translateY(-1px); box-shadow:0 10px 20px rgba(30,57,108,0.3); border-right-color:transparent;}
.coreStrengthInnerBox:hover .coreStrengthInnerBoxIcon{background:#fff;}
.coreStrengthInnerBox:hover .coreStrengthInnerBoxIcon img{filter:initial;}
.coreStrengthInnerBox:hover .coreStrengthInnerBoxContent h3,.coreStrengthInnerBox:hover .coreStrengthInnerBoxContent p{color:#fff;}
.our-partners{padding:80px 0; background:#fff; position:relative; overflow:hidden;}
.our-partners h2{color:var(--primary); font-size:40px; line-height:44px; font-weight:700; margin-bottom:50px; text-align:center;}
.our-partners-slider .item{display:flex; align-items:center; justify-content:center; padding:10px;}
.our-partners-slider .item img{height:auto; opacity:1;  transition:all 0.4s ease-in-out;}
.our-partners-slider .item:hover img{transform:scale(1.1); opacity:1; filter:grayscale(0%);}
.our-process{background:#fff; padding-bottom:60px; position:relative;}
.our-process h3{color:var(--secondary); text-transform:uppercase; font-size:14px; line-height:15px; font-weight:600; margin-bottom:10px;}
.our-process h2{color:var(--primary); font-size:40px; line-height:44px; font-weight:700;}
.our-process h4{color:var(--secondary); font-size:28px; line-height:1.2; font-weight:600; margin:30px 0;}
.our-process h4 strong{font-weight:700;}
.our-process p{color:var(--primary); font-size:18px; line-height:1.6; font-weight:500; margin-bottom:0;}
.strategic-alliance{background:#eeeeee; padding:80px 0; position:relative; overflow:hidden;z-index:1;}
.strategic-alliance::before{content:""; position:absolute; top:0; right:-230px; width:100%; height:100%; background:url(assets/img/logo-cut-about.svg) top right no-repeat; z-index:-1; display:block;}
.strategic-alliance h3{color:var(--secondary); text-transform:uppercase; font-size:14px; line-height:15px; font-weight:600; margin-bottom:10px;}
.strategic-alliance h2{color:#397c3c; font-size:40px; line-height:44px; font-weight:700; margin-bottom:30px;}
.strategic-alliance p{color:var(--primary); font-size:18px; line-height:1.6; font-weight:500; margin-bottom:0;}
.about-our-story{background:var(--primary); padding:80px 0; text-align:center; position:relative;}
.about-our-story h3{color:#fff; text-transform:uppercase; font-size:14px; line-height:15px; font-weight:600; margin-bottom:10px;}
.about-our-story h2{color:#fff; font-size:40px; line-height:44px; font-weight:700; margin-bottom:30px;}
.about-our-story p{color:#fff; font-size:18px; line-height:1.6; font-weight:500; text-align:left; margin-bottom:0;}
.about-our-team{padding:80px 0 60px; background:#fff; position:relative; overflow:hidden;}
.about-team-head h3{color:var(--secondary); text-transform:uppercase; font-size:14px; line-height:15px; font-weight:600; margin-bottom:10px; text-align:center;}
.about-team-head h2{color:var(--primary); font-size:40px; line-height:44px; font-weight:700; text-align:center; margin-bottom:60px;}
.about-team-head p{color:var(--primary); font-size:18px; line-height:1.6; font-weight:500; margin-bottom:0; margin-bottom:40px;}
.about-our-team button{border:none; background:none; outline:none;}
.team-box{position:relative; overflow:hidden; text-align:center; margin-bottom:20px;}
.team-box-img{position:relative; overflow:hidden;}
.team-overlay{position:absolute; bottom:-100%; left:0; width:100%; height:100%; background:rgb(30 57 108 / 70%); transition:0.3s ease-in-out; display:flex; align-items:center; justify-content:center; opacity:0;}
.team-box:hover .team-overlay{bottom:0; opacity:1;}
.team-social{list-style:none; margin:0; padding:0; display:flex; gap:10px; opacity:0; transform:translateY(10px); transition:0.4s ease 0.35s; /* delay after overlay */}
.team-box:hover .team-social{opacity:1; transform:translateY(0px);}
.team-social li{display:inline-block;}
.team-social li a{width:30px; height:30px; border-radius:50%; background:#fff; display:flex; align-items:center; justify-content:center; text-decoration:none; color:var(--primary); font-size:14px; transition:0.3s ease;}
.team-social li a:hover{background:var(--primary); color:#fff;}
.team-box-content{margin-top:20px;}
.team-box-content h3{color:var(--primary); font-size:21px; line-height:24px; font-weight:700; margin-bottom:10px;}
.team-box-content p{color:var(--secondary); font-size:14px; line-height:18px; font-weight:500; margin-bottom:0;}
.team-modal h5{color:var(--primary); font-size:21px; line-height:24px; font-weight:700; margin-bottom:10px;}
/*--------------------------------------------------------------
# Contact Us
--------------------------------------------------------------*/
.contactUs{position:relative; z-index:1; overflow:hidden;}
.contactUs::before{content:""; position:absolute; top:0; left:0; width:100%; height:100%; background:rgb(255 255 255 / 70%); z-index:-1; display:block;}
.contactUs h3{color:#000; text-transform:uppercase; font-size:14px; line-height:15px; font-weight:600; text-transform:uppercase; margin-bottom:10px;}
.contactUs h2{color:var(--primary); font-size:40px; line-height:44px; font-weight:700; margin-bottom:40px;}
.contactUs .contactList li{display:flex; gap:20px; margin-bottom:25px;}
.contactUs .contactList li i{color:var(--primary); font-size:20px;}
.contactUs .contactList li h4{color:var(--primary); text-transform:uppercase; font-size:16px; line-height:16px; font-weight:700; margin-bottom:15px;}
.contactUs ul li a{color:var(--primary); font-size:14px; line-height:14px; font-weight:500;}
.contactFormSec{display:flex; flex-direction:column; justify-content:space-between; align-items:flex-end; height:100%;}
.contactForm .form-fields,.contactForm .form-fields2,.contactForm .form-fields3{background:#fff; color:var(--primary); margin-bottom:0;}
.contactForm ::placeholder{color:var(--primary);}
.socialIcons li a{color:var(--primary); width:35px; height:35px; display:flex; justify-content:center; align-items:center; border-radius:50%;}
.socialIcons li a:hover{color:#fff; background:var(--primary);}
/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/


.footer{position:relative; z-index:1; background:var(--primary); padding:100px 0 40px; overflow:hidden;}
.footer::before{content:""; position:absolute; top:0; right:-300px; width:100%; height:100%; background:url(assets/img/f-logo-cut.svg) top right no-repeat; z-index:-1; display:block;}
.footer .footerCol1 img{width:250px;}
.footer h4{color:#fff; text-transform:uppercase;}
.footer .footerCol2 ul li a{color:#fff; font-size:14px; font-weight:600; line-height:30px; position:relative; text-transform:uppercase;}
.footer .footerCol2 ul li a:hover{font-weight:700;}
.footerCol3 ul{list-style:none; padding:0; margin:0;}
.footerCol3 ul li{display:flex; align-items:flex-start; gap:10px; margin-bottom:12px; line-height:22px; transition:all 0.3s ease;}
.footerCol3 ul li i{font-size:16px; color:#fff; min-width:18px; margin-top:3px;}
.footerCol3 ul li a,.footerCol3 ul li span{font-size:15px; color:#fff; text-decoration:none;}
.footerCol3 ul li:hover a,.footerCol3 ul li:hover i,.footerCol3 ul li:hover span{color:var(--primary);}
.copyRight{margin:80px 0 0;}
.copyRight p{color:#fff; font-size:12px; line-height:15px; margin-bottom:0;}
.copyRight p a{color:#fff;}
.pt-6{padding-top:5rem !important;}
.pb-6{padding-bottom:5rem !important;}
.py-6{padding-top:5rem !important; padding-bottom:5rem !important;}
.py-7{padding-top:7rem !important; padding-bottom:7rem !important;}
.form-fields,.form-fields2,.form-fields3{width:100% !important; box-sizing:border-box; padding:10px 14px; font-size:15px; margin-bottom:15px; background:none; color:#ffffff; border:1px solid #ffffff; text-transform:capitalize; -webkit-transition:0.3s ease-in-out !important; -moz-transition:0.3s ease-in-out !important; -ms-transition:0.3s ease-in-out !important; -o-transition:0.3s ease-in-out !important; transition:0.3s ease-in-out !important;}
.form-fields:focus,.form-fields2:focus,.form-fields3:focus{border:none; border:1px solid #ffffff; outline:none !important;}
.form-fields3{height:100px;}
.wpcf7-submit,.ln-widgetBox.search .searchButton a{position:relative; padding:12px 30px; font-weight:700; display:inline-block; text-transform:uppercase; border:1px solid var(--primary); background:var(--primary); color:#ffffff; font-size:14px; transition:all 0.3s ease !important;}
.wpcf7-submit:hover{border:1px solid #fff; background:#fff; color:var(--primary);}
div.wpcf7 img.ajax-loader{float:left;}
.wpcf7-list-item{display:inline-block; margin-right:10px;}
div.wpcf7-response-output{float:left;}
.wpcf7-not-valid-tip{display:none !important;}
.wpcf7-not-valid{border-bottom:2px solid red !important; border:none;}
::placeholder{font-size:14px; text-transform:uppercase; color:#ffffff;}
.wpcf7 form.invalid .wpcf7-response-output,.wpcf7 form.unaccepted .wpcf7-response-output,.wpcf7 form.payment-required .wpcf7-response-output{border-color:#ffb900; color:#ffffff;}
.modal-header h1{text-transform:uppercase; font-weight:700;}
.modal-body .form-fields,.modal-body .form-fields2,.modal-body .form-fields3{border-bottom:1px solid #cccccc; color:#000000; font-weight:500;}
.modal-body::placeholder{color:#000000 !important; font-weight:500; font-size:12px;}
.modal-body .wpcf7 form.invalid .wpcf7-response-output,.modal-body .wpcf7 form.unaccepted .wpcf7-response-output,.modal-body .wpcf7 form.payment-required .wpcf7-response-output{border-color:#ffb900; color:#000000;}
@media (min-width:1400px){
  .container,.container-lg,.container-md,.container-sm,.container-xl,.container-xxl{max-width:1140px;}
}

/*--------------------------------------------------------------
# Inner Pages
--------------------------------------------------------------*/
.pageBanner{position:relative; width:100%; height:50vh; text-align:start; background-attachment:fixed !important;}
.pageBanner::before{content:""; position:absolute; inset:0; width:100%; height:100%; background:rgba(30, 57, 108 , 0.7); display:block;}
.pageBannerInner{position:relative; text-align:center; padding-top:10%;}
.pageBanner .pageBannerInner .serviceIcon{font-size:2rem; margin-bottom:1rem; padding-bottom:.5rem; display:inline-block; border-bottom:2px solid #ffffff;}
.pageBanner .pageBannerInner h1{font-size:42px; line-height:50px; text-transform:uppercase; font-weight:500; color:#ffffff; text-align:center; margin-bottom:0;}


/*--------------------------------------------------------------
# Contact
--------------------------------------------------------------*/
.cAddSec1 .cAddSec1LeftCol h4{font-weight:700; color:var(--primary);}
.cAddSec1 .cAddSec1LeftCol h2{font-size:50px; line-height:50px;}
.cAddSec1 .cAddSec1LeftCol p{font-size:18px; line-height:26px; margin:0;}
.cAddSec1 .cAddSec1RightCol .form-fields,.cAddSec1 .cAddSec1RightCol .form-fields2,.cAddSec1 .cAddSec1RightCol .form-fields3{border-color:#000000; color:#000000; margin-bottom:0; padding:8px 14px;}
.cAddSec1 .cAddSec1RightCol input::placeholder{color:#000000;}
.cAddSec1 .cAddSec1RightCol textarea::placeholder{color:#000000;}
.cAddSec2{position:relative; padding-top:60px; padding-bottom:60px;}
.contact-details__info{position:relative; display:block; padding:50px 40px; background-color:#f3f3f3; border:1px solid #bbbbbb;}
.contact-details__info li{position:relative; display:flex; align-items:center;}
.contact-details__info li .icon{height:80px; width:80px; border-radius:50%; background-color:#ffffff; display:flex; align-items:center; justify-content:center; transition:all .5s ease;}
.contact-details__info .icon{background:#ffffff;}
.contact-details__info li:hover .icon{background:var(--primary);}
.contact-details__info li .icon i{font-size:2rem; color:var(--primary);}
.contact-details__info li:hover .icon i{color:#ffffff;}
.contact-details__info li .text{margin-left:15px;}
.contact-details__info li .text h6{margin:0; font-size:22px; line-height:1.1;}
.contact-details__info li .text a,.contact-details__info li .text span{font-size:13px;}
.contact-details__info li .text span br{display:none;}
.cAddSec1RightCol .wpcf7-submit:hover{background:var(--primary); border:1px solid var(--primary); color:#fff; font-weight:700;}
.contactFormSec label {color: var(--primary); text-transform: uppercase; font-size: 15px;}
.page-template-contact-us #header , .page-template-about #header{background: var(--primary);} 
.page-template-contact-us #header .navbar li:last-child a , .page-template-about #header .navbar li:last-child a{border-color: #ffffff;}

/*--------------------------------------------------------------
# SnapCRE Properties
--------------------------------------------------------------*/
.single-properties #header , .page-template-listings #header{background:var(--primary);}
.single-properties #header .navbar li a, .single-properties .navbar li a:focus , .page-template-listings #header .navbar li a, .page-template-listings .navbar li a:focus  {    color: #ffffff;}
.single-properties #header .navbar li:last-child a , .page-template-listings #header .navbar li:last-child a {border-color: #fff;}
.fcre-single-property , .page-template-listings{margin-top: 150px;}
.fcre-signle-property-heading h1 , .fcre-single-property-title , .fcre-single-property-description ul li:before , .fcre-listing-box h2{color: var(--primary);  text-transform:initial;}
.fcre-signle-property-heading h1 {font-size: 40px;line-height: 44px;font-weight: 700;text-transform:initial;}
.fcre-single-property-overview .fcre-single-property-overview-item{border-bottom: 1px solid var(--primary);}
.fcre-btn-primary, .fcre-btn, .fcre-filter-btn, .fcre-submit-btn, .filter-submit, button.fcre-btn-primary, input[type=submit].fcre-btn{background-color: var(--primary);
    border-color: var(--primary);}
.fcre-tab-btn.active, .fcre-tab-titles li.active {
    border-color: var(--primary);
    color: var(--primary);
}
.fcre-view-toggle-btn.active svg{filter: brightness(0) invert(1);}
.fcre-listing-content h4{color:#fff}

.page-template-listings .fcre-filter-wrapper .fcre-col-xl-2.fcre-col-lg-4.fcre-col-md-6.fcre-col-sm-12:nth-child(1)
, .page-template-listings .fcre-filter-wrapper .fcre-col-xl-2.fcre-col-lg-4.fcre-col-md-6.fcre-col-sm-12:nth-child(5)
, .page-template-listings .fcre-filter-wrapper .fcre-col-xl-2.fcre-col-lg-4.fcre-col-md-6.fcre-col-sm-12:nth-child(6){display: none;}
.page-template-listings .fcre-filter-wrapper .fcre-col-xl-2.fcre-col-lg-4.fcre-col-md-6.fcre-col-sm-12:nth-child(2) , 
.page-template-listings .fcre-filter-wrapper .fcre-col-xl-2.fcre-col-lg-4.fcre-col-md-6.fcre-col-sm-12:nth-child(3) , 
.page-template-listings .fcre-filter-wrapper .fcre-col-xl-2.fcre-col-lg-4.fcre-col-md-6.fcre-col-sm-12:nth-child(4) {width: 33%;} 
/*--------------------------------------------------------------
# Media Queries
--------------------------------------------------------------*/
@media (max-width:1440px){
  #header{padding:25px 5%;}
  .features .box.top-left,.features .box.bottom-left{padding-inline:5% 30%;}
  .features .box.top-right,.features .box.bottom-right{padding-inline:30% 5%;}
  .features .whyUsIcon{width:350px;}
  .newsLetter {padding-inline: 2%;}
  .newsLetter h2 {font-size: 34px;line-height: 38px;}
}
@media (max-width:768px){
  .page-template-listings .fcre-filter-wrapper .fcre-col-xl-2.fcre-col-lg-4.fcre-col-md-6.fcre-col-sm-12:nth-child(2), .page-template-listings .fcre-filter-wrapper .fcre-col-xl-2.fcre-col-lg-4.fcre-col-md-6.fcre-col-sm-12:nth-child(3), .page-template-listings .fcre-filter-wrapper .fcre-col-xl-2.fcre-col-lg-4.fcre-col-md-6.fcre-col-sm-12:nth-child(4){width: 100%;}
  }
@media (max-width:481px){
  }
@media (max-width:320px){
  }
@media (min-width:1280px) and (max-width:1366px){
  }

/*--------------------------------------------------------------
# Login Modal
--------------------------------------------------------------*/
.login-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-modal-content {
  background: #fff;
  width: 90%;
  max-width: 450px;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  position: relative;
}

.login-modal-header {
  background: var(--primary);
  color: #fff;
  padding: 20px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  line-height: 1;
}

.login-modal-close {
  background: none;
  border: none;
  color: #fff;
  font-size: 28px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.login-modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.login-modal-body {
  padding: 30px 25px;
}

.login-modal-body .form-group {
  margin-bottom: 20px;
}

.login-modal-body .form-group:last-child {
  margin-bottom: 0;
}

.login-modal-body label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--primary);
  font-size: 14px;
}

.login-modal-body input[type="text"],
.login-modal-body input[type="password"] {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.login-modal-body input[type="text"]:focus,
.login-modal-body input[type="password"]:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(30, 57, 108, 0.1);
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  cursor: pointer;
  font-size: 13px;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 8px;
  width: auto;
}

.login-btn {
  width: 100%;
  background: var(--primary);
  color: #fff;
  border: none;
  padding: 12px 20px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-btn:hover {
  background: #1a3159;
}

.login-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.login-error {
  background: #f8d7da;
  color: #721c24;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  border: 1px solid #f5c6cb;
}

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden;
}

/* Mobile responsive */
@media (max-width: 480px) {
  .login-modal-content {
    width: 95%;
    margin: 20px;
  }

  .login-modal-header,
  .login-modal-body {
    padding: 20px;
  }
}