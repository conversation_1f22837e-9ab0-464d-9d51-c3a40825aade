<?php
/**
 * The template for displaying single Virtual Data Room posts
 *
 * @package WordPress
 * @subpackage Corner_Realty
 */

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(home_url());
    exit;
}

get_header();
?>

<div class="pageMargin">
    <?php while (have_posts()) : the_post(); ?>
        <div class="pageBanner" style="background: url('<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/videoposter.jpg') center center/cover;">
            <div class="pageBannerInner">
                <div class="container-xl">
                    <div class="row">
                        <div class="col-12">
                            <h1><?php the_title(); ?></h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="single-vdr-content py-6">
            <div class="container-xl">
                <div class="row">
                    <div class="col-lg-8 col-12">
                        <div class="vdr-main-content">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="vdr-featured-image mb-4">
                                    <?php the_post_thumbnail('large', array('class' => 'img-fluid')); ?>
                                </div>
                            <?php endif; ?>

                            <div class="vdr-content">
                                <?php the_content(); ?>
                            </div>

                            <?php
                            // Display file attachments
                            $attachments = get_attached_media('', get_the_ID());
                            if ($attachments) : ?>
                                <div class="vdr-attachments mt-5">
                                    <h3>Available Files</h3>
                                    <div class="attachments-list">
                                        <?php foreach ($attachments as $attachment) :
                                            $file_url = wp_get_attachment_url($attachment->ID);
                                            $file_name = get_the_title($attachment->ID);
                                            $file_size = size_format(filesize(get_attached_file($attachment->ID)));
                                            $file_type = wp_check_filetype($file_url);
                                        ?>
                                            <div class="attachment-item">
                                                <div class="attachment-info">
                                                    <h4><?php echo esc_html($file_name); ?></h4>
                                                    <p class="file-meta">
                                                        Type: <?php echo esc_html(strtoupper($file_type['ext'])); ?> | 
                                                        Size: <?php echo esc_html($file_size); ?>
                                                    </p>
                                                </div>
                                                <div class="attachment-actions">
                                                    <a href="<?php echo esc_url($file_url); ?>" 
                                                       class="btn btn-primary" 
                                                       target="_blank">
                                                        View
                                                    </a>
                                                    <a href="<?php echo esc_url($file_url); ?>" 
                                                       class="btn btn-outline-primary" 
                                                       download="<?php echo esc_attr($file_name); ?>">
                                                        Download
                                                    </a>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-lg-4 col-12">
                        <div class="vdr-sidebar">
                            <div class="vdr-meta-box">
                                <h4>Document Information</h4>
                                <ul class="vdr-meta-list">
                                    <li>
                                        <strong>Published:</strong> 
                                        <?php echo get_the_date(); ?>
                                    </li>
                                    <li>
                                        <strong>Last Updated:</strong> 
                                        <?php echo get_the_modified_date(); ?>
                                    </li>
                                    <?php if (get_the_category()) : ?>
                                        <li>
                                            <strong>Category:</strong> 
                                            <?php the_category(', '); ?>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </div>

                            <div class="vdr-navigation">
                                <h4>Navigation</h4>
                                <a href="<?php echo home_url('/virtual-data-room/'); ?>" class="btn btn-outline-primary btn-block">
                                    ← Back to Virtual Data Room
                                </a>
                            </div>

                            <?php
                            // Show related VDR posts
                            $related_posts = new WP_Query(array(
                                'post_type' => 'virtual-data-room',
                                'posts_per_page' => 3,
                                'post__not_in' => array(get_the_ID()),
                                'orderby' => 'rand'
                            ));

                            if ($related_posts->have_posts()) : ?>
                                <div class="vdr-related">
                                    <h4>Related Documents</h4>
                                    <div class="related-posts">
                                        <?php while ($related_posts->have_posts()) : $related_posts->the_post(); ?>
                                            <div class="related-post-item">
                                                <h5>
                                                    <a href="<?php the_permalink(); ?>">
                                                        <?php the_title(); ?>
                                                    </a>
                                                </h5>
                                                <small class="text-muted">
                                                    <?php echo get_the_date(); ?>
                                                </small>
                                            </div>
                                        <?php endwhile; ?>
                                    </div>
                                </div>
                            <?php endif; 
                            wp_reset_postdata(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endwhile; ?>
</div>

<style>
.single-vdr-content {
    background: #fff;
}

.vdr-content {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
}

.vdr-content h2,
.vdr-content h3,
.vdr-content h4 {
    color: var(--primary);
    margin-top: 30px;
    margin-bottom: 15px;
}

.vdr-attachments {
    border-top: 2px solid var(--primary);
    padding-top: 30px;
}

.vdr-attachments h3 {
    color: var(--primary);
    margin-bottom: 20px;
}

.attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 15px;
    background: #f9f9f9;
}

.attachment-info h4 {
    margin: 0 0 5px 0;
    color: var(--primary);
    font-size: 16px;
}

.file-meta {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.attachment-actions {
    display: flex;
    gap: 10px;
}

.vdr-sidebar {
    padding-left: 30px;
}

.vdr-meta-box,
.vdr-navigation,
.vdr-related {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.vdr-meta-box h4,
.vdr-navigation h4,
.vdr-related h4 {
    color: var(--primary);
    margin-bottom: 20px;
    font-size: 18px;
}

.vdr-meta-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.vdr-meta-list li {
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.vdr-meta-list li:last-child {
    border-bottom: none;
}

.btn-block {
    width: 100%;
    text-align: center;
}

.related-post-item {
    padding: 15px 0;
    border-bottom: 1px solid #e0e0e0;
}

.related-post-item:last-child {
    border-bottom: none;
}

.related-post-item h5 {
    margin: 0 0 5px 0;
    font-size: 14px;
}

.related-post-item a {
    color: var(--primary);
    text-decoration: none;
}

.related-post-item a:hover {
    text-decoration: underline;
}

@media (max-width: 991px) {
    .vdr-sidebar {
        padding-left: 0;
        margin-top: 40px;
    }
    
    .attachment-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .attachment-actions {
        width: 100%;
        justify-content: flex-start;
    }
}
</style>

<?php get_footer(); ?>
