<?php

/**
 * Template Name: About
 */
get_header();
global $post;
$meta      = get_field_objects($post->ID);
$home_meta = get_field_objects(9);
$ids = get_post_meta($post->ID, 'vdw_gallery_id', true);
$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>

<main id="aboutMain">
	<section>
		<div class="aboutUs pageMargin">
			<div class="container-xl">
				<div class="row">
					<div class="col-md-12">
						<div data-aos="fade-right">
							<?php the_content(); ?>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<section>
		<div class="whatSetsUsApart">
			<video autoplay muted loop playsinline>
				<source src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/corner-realty-home-videos-lowres-20251310.mp4" type="video/mp4">
			</video>
			<div class="container-xl position-relative text-center text-white" style="z-index: 2;">
				<div class="whatSetsUsApartHead" data-aos="fade-down">
					<h3>Who we are</h3>
					<h2>What Sets Us Apart</h2>
				</div>
				<div class="whatSetsUsApartInner">
					<div class="whatSetsUsApartBox" data-aos="fade-right">
						<h3>Focused</h3>
					</div>
					<div class="whatSetsUsApartBox middle" data-aos="fade-up">
						<h3>Strategic</h3>
					</div>
					<div class="whatSetsUsApartBox" data-aos="fade-left">
						<h3>Results-Driven</h3>
					</div>
				</div>
			</div>
		</div>
	</section>

	<section>
		<div class="coreStrength">
			<div class="container-xl">
				<div class="coreStrengthInner">
					<?php
					$args = query_posts(
						array(
							'post_type' => 'core-strength',
							'category_name' => '',
							'order' => 'ASC',
							'posts_per_page' => -1
						)
					);
					if (have_posts()) {
						$delay = 100;
						while (have_posts()) :
							the_post();
							$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');

					?>
							<!-- add Aos Animation to boxes -->
							<div class="coreStrengthInnerBox" data-aos="fade-up" data-aos-delay="<?php echo $delay += 100; ?>">
								<div class="coreStrengthInnerBoxIcon">
									<img class="img-fluid" src="<?php echo $image[0]; ?>" alt="">
								</div>
								<div class="coreStrengthInnerBoxContent">
									<h3><?php the_title(); ?></h3>
									<?php the_content(); ?>
								</div>
							</div>
					<?php
							$delay += 100;
						endwhile;
					}
					wp_reset_query();
					?>
				</div>
			</div>
		</div>
	</section>

	<section>
		<div class="our-partners">
			<div class="container-xl">
				<div class="row">
					<div class="col-md-12">
						<h2>
							We are trusted by nearly every major consolidator,
							ensuring unmatched access to decision-makers.
						</h2>
					</div>
				</div>
				<div class="row">
					<div class="our-partners-slider owl-carousel owl-theme">
						<?php
						if ($ids) {
							foreach ($ids as $key => $value) {
								$image = wp_get_attachment_image_src($value, 'medium');
						?>
								<div class="item">
									<img src="<?php echo $image[0]; ?>" alt="partner logo" />
								</div>
						<?php
							}
						}
						?>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- Our Process -->
	<section>
		<div class="our-process">
			<div class="container-xl">
				<div class="row">
					<div class="col-md-12">
						<div data-aos="fade-right">
							<h3>About Us</h3>
							<h2>Our Process</h2>
							<h4><strong>Every engagement is unique — and so is our approach. </strong></h4>
							<p>At Corner Realty, we tailor our strategies to the distinct needs of each client. Grounded in deep industry expertise,
								our process is executed with precision from start to finish — from underwriting and valuation to strategic
								outreach and structured negotiations.</p>
							<h4>Our objective is simple: <strong>unlock full market value while maintaining the highest level of discretion.</strong></h4>
							<p>We operate as an extension of your team, managing every step of the process so you can stay focused on
								running your business. Our integrated understanding of operations, capital structures, and real estate ensures
								no stone is left unturned when it comes to maximizing outcomes and positioning your company for long-term
								success.</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- A Strategic Alliance -->

	<section>
		<div class="strategic-alliance">
			<div class="container-xl">
				<div class="row">
					<div class="col-md-12">
						<div data-aos="fade-right">
							<h3>About Us</h3>
							<h2>A Strategic Alliance</h2>
							<p>Our affiliation with Corner Capital Group enhances our capabilities through shared market intelligence, ex panded buyer reach and synergistic advisory services. This strategic relationship enables us to deliver a fully integrated platform, combining investment banking discipline with real estate insight, tailored to the unique dynamics of the gas & convenience retail industry.</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- Our Story -->
	<section>
		<div class="about-our-story">
			<div class="container-xl">
				<div class="row">
					<div class="col-md-12">
						<div data-aos="fade-right">
							<h3>Who we are</h3>
							<h2>Our Story</h2>
							<p>Born out of deep industry experience and a shared commitment to doing things the right way, our firm
								was founded to serve the specific needs of business owners in the retail fuel and downstream energy
								space. With backgrounds that span finance, operations, and commercial real estate, our team understands
								the full scope of what drives value, and how to position your business to realize it.</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<section>
		<div class="about-our-team">
			<div class="container-xl">
				<div class="row about-team-head">
					<div class="col-md-12">
						<div data-aos="fade-down">
							<h3>Team Members</h3>
							<h2>Meet our Team</h2>
							<p>
								<strong>Our people define our firm. </strong> Each advisor brings a unique combination of transactional experience, operational insight, and strategic
								perspective — united by a shared mission: to deliver excellence at every stage of the process.
							</p>
						</div>
					</div>
				</div>
				<div class="row">


					<?php
					$args = query_posts(
						array(
							'post_type' => 'team',
							'category_name' => '',
							'order' => 'ASC',
							'posts_per_page' => -1
						)
					);
					if (have_posts()) {
						$delay = 100;
						while (have_posts()) :
							the_post();
							$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
							$designation = get_field('designation');
							$phone = get_field('phone');
							$email = get_field('email');
							$facebook = get_field('facebook');
							$linkedin = get_field('linkedin');

					?>
							<div class="col-md-4">
								<button data-bs-toggle="modal" data-bs-target="#teamModal-<?php echo $post->ID; ?>">
									<div class="team-box" data-aos="fade-right" data-aos-delay="<?php echo $delay += 100; ?>">
										<div class="team-box-img">
											<img class="img-fluid" src="<?php echo $image[0]; ?>" alt="">

											<!-- Hover Overlay + Social Icons -->
											<div class="team-overlay">
												<ul class="team-social">
													<?php if ($facebook) { ?>
														<li><a href="<?php echo $facebook; ?>"><i class="fa-brands fa-facebook-f"></i></a></li>
													<?php }
													if ($phone) { ?>

														<li><a href="tel:<?php echo $phone; ?>"><i class="fa-solid fa-phone"></i></a></li>
													<?php }
													if ($email) { ?>

														<li><a href="mailto:<?php echo $email; ?>"><i class="fa-regular fa-envelope"></i></a></li>
													<?php }
													if ($linkedin) { ?>

														<li><a href="<?php echo $linkedin; ?>"><i class="fa-brands fa-linkedin-in"></i></a></li>
													<?php } ?>

												</ul>
											</div>
										</div>
										<div class="team-box-content">
											<h3><?php the_title(); ?></h3>
											<p><?php echo $designation; ?></p>
										</div>
									</div>
								</button>
							</div>
					<?php
							$delay += 100;
						endwhile;
					}
					wp_reset_query();
					?>
				</div>
			</div>
		</div>
	</section>
	<?php
	$args = query_posts(
		array(
			'post_type' => 'team',
			'category_name' => '',
			'order' => 'ASC',
			'posts_per_page' => -1
		)
	);
	if (have_posts()) {
		while (have_posts()) :
			the_post();
			$image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
			$designation = get_field('designation');

	?>
			<div class="modal team-modal fade" id="teamModal-<?php echo $post->ID; ?>" tabindex="-1" aria-labelledby="teamModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
						<div class="modal-header">
							<h5 class="modal-title" id="teamModalLabel"><?php the_title(); ?></h5>


							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<div class="row">
								<div class="col-md-12">
									<?php the_content(); ?>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
	<?php
		endwhile;
	}
	wp_reset_query();
	?>



</main>


<?php get_footer(); ?>

<!-- Owl Carousel CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">

<!-- jQuery (Owl needs this) -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<!-- Owl Carousel JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
<script>
	$(".our-partners-slider").owlCarousel({
		loop: true,
		margin: 10,
		nav: false,
		center: true,
		dots: false,
		autoplay: true,
		autoplayTimeout: 2400, // Slide change time (in milliseconds)
		autoplaySpeed: 900, // Transition speed (in milliseconds)
		navText: [
			'<span class="fa-solid fa-arrow-right-long left"></span>',
			'<span class="fa-solid fa-arrow-right-long"></span>',
		],
		responsive: {
			0: {
				items: 1
			},
			320: {
				items: 2
			},
			480: {
				items: 3
			},
			576: {
				items: 4
			},
			768: {
				items: 5
			}
		},
	});
</script>